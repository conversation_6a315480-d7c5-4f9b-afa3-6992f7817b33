# Graphics Structure Guide - Modern Front 2025 v2
## Professional HOI4 Mod Graphics Implementation

### 📁 **Directory Structure Created:**
```
Modern Front 2025 v2/gfx/
├── flags/                    # Country flags (82x52 pixels, .tga format)
│   ├── USA.tga              # United States flag
│   ├── CHI.tga              # China flag
│   ├── RUS.tga              # Russia flag
│   ├── GER.tga              # Germany flag
│   ├── ENG.tga              # United Kingdom flag
│   ├── FRA.tga              # France flag
│   ├── JAP.tga              # Japan flag
│   └── ITA.tga              # Italy flag
├── leaders/                  # Leader portraits
│   └── MODERN_2025/         # Modern leaders subfolder
│       ├── USA_trump_2025.dds       # Trump portrait (156x210 pixels)
│       ├── CHI_xi_jinping_2025.dds  # Xi Jinping portrait
│       ├── RUS_putin_2025.dds       # Putin portrait
│       ├── GER_merz_2025.dds        # Merz portrait
│       ├── ENG_starmer_2025.dds     # Starmer portrait
│       ├── FRA_macron_2025.dds      # Macron portrait
│       ├── JAP_ishiba_2025.dds      # Ishiba portrait
│       └── ITA_meloni_2025.dds      # Meloni portrait
└── interface/               # UI elements
    └── select_date_2025.dds # Scenario selection background
```

### 🎨 **File Format Requirements:**

#### **Country Flags:**
- **Format:** `.tga` (Targa format)
- **Resolution:** 82x52 pixels (standard HOI4 flag size)
- **Color Mode:** RGB, 24-bit
- **Naming:** `[COUNTRY_TAG].tga` (e.g., USA.tga, CHI.tga)

#### **Leader Portraits:**
- **Format:** `.dds` (DirectDraw Surface)
- **Resolution:** 156x210 pixels (standard HOI4 leader portrait size)
- **Compression:** DXT5 recommended for quality with transparency
- **Color Mode:** RGB with Alpha channel
- **Naming:** `[COUNTRY]_[leader_name]_2025.dds`

#### **Interface Elements:**
- **Format:** `.dds` (DirectDraw Surface)
- **Resolution:** Varies by element
- **Compression:** DXT5 for images with transparency, DXT1 for opaque

### 🔧 **Implementation Steps:**

#### **Step 1: Country Flags**
1. Source current 2025 flags for each country
2. Resize to 82x52 pixels exactly
3. Save as .tga format in `gfx/flags/`
4. Use country tags: USA, CHI, RUS, GER, ENG, FRA, JAP, ITA

#### **Step 2: Leader Portraits**
1. Source high-quality photos of current leaders
2. Crop to portrait orientation (3:4 ratio)
3. Resize to 156x210 pixels exactly
4. Convert to .dds format with DXT5 compression
5. Place in `gfx/leaders/MODERN_2025/`

#### **Step 3: Update Character Files**
Update portrait paths in character files:
```
portraits = {
    civilian = {
        large = "gfx/leaders/MODERN_2025/[COUNTRY]_[leader]_2025.dds"
    }
}
```

### 📋 **Required Files Checklist:**

#### **Flags (8 files):**
- [ ] USA.tga - Current US flag
- [ ] CHI.tga - People's Republic of China flag
- [ ] RUS.tga - Russian Federation flag
- [ ] GER.tga - Federal Republic of Germany flag
- [ ] ENG.tga - United Kingdom flag
- [ ] FRA.tga - French Republic flag
- [ ] JAP.tga - Japan flag
- [ ] ITA.tga - Italian Republic flag

#### **Leader Portraits (8 files):**
- [ ] USA_trump_2025.dds - Donald Trump
- [ ] CHI_xi_jinping_2025.dds - Xi Jinping
- [ ] RUS_putin_2025.dds - Vladimir Putin
- [ ] GER_merz_2025.dds - Friedrich Merz
- [ ] ENG_starmer_2025.dds - Keir Starmer
- [ ] FRA_macron_2025.dds - Emmanuel Macron
- [ ] JAP_ishiba_2025.dds - Shigeru Ishiba
- [ ] ITA_meloni_2025.dds - Giorgia Meloni

### ⚠️ **Important Notes:**
- **File names are case-sensitive** in HOI4
- **Exact pixel dimensions required** - game will not scale properly
- **DDS compression matters** - use DXT5 for portraits, DXT1 for flags
- **Alpha channels** should be properly handled for transparency
- **Test in-game** after adding each file to verify display

### 🛠️ **Recommended Tools:**
- **GIMP** (free) with DDS plugin
- **Paint.NET** (free) with DDS plugin
- **Adobe Photoshop** with NVIDIA DDS plugin
- **ImageMagick** for batch conversion
- **Online converters** (less recommended for quality)

### 🎯 **Expected Results:**
- All 8 countries display proper 2025 flags
- All 8 leaders show actual portraits instead of silhouettes
- Professional appearance matching real-world 2025 political landscape
- No crashes or missing texture errors
